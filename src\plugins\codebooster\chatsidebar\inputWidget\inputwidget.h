#ifndef INPUTWIDGET_H
#define INPUTWIDGET_H

#include <QPlainTextEdit>
#include <QResizeEvent>
#include <QPushButton>
#include <QLabel>
#include <QToolBar>
#include <QToolButton>
#include <QCheckBox>

#include <solutions/spinner/spinner.h>

#include "chatcontext/contextitem.h"
#include "codesnippetwidget.h"
#include "customtextedit.h"

namespace CodeBooster::Internal{

// -------------------------------------------------------------------------------
// -------------------------------------------------------------------------------

class ContextItemContainer;

/**
 * @brief The InputWidget class 输入控件区域
 */
class InputWidget : public QFrame
{
    Q_OBJECT

public:
    InputWidget(QWidget *parent = nullptr);
    ~InputWidget();

    static bool defaultShowEditorSelection() {return true;}

public:
    void waitingForReceiveMsg();
    void messageReceiveFinished();
    void setShowEditorSelection(bool show);

    void activateInput();

    void setText(const QString &text);
    void onSendButtonClicked();
    void currentRequestStoped();

    QList<ContextItem> contexts() const;

protected:
    bool event(QEvent *event) override;

signals:
    void sendUserMessage(const QString &message, const QList<ContextItem> &contexts);
    void createNewChat();
    void inputFocusModeChanged(bool enable);
    void showContextBuilder();

private slots:
    void onShowCodeSnippet(const QString &fileName, const QString &text, int startLine, int endLine);
    void onTextEditFocusChange(bool focus);
    void onUseCurrentFileClicked(bool checked);

private:
    CodeSnippetWidget* createNewCodeSnippetWidget(const QString &fileName, const QString &text, int startLine = 1, int endLine = 1);

private:
    QFrame *mMainInputContainer;
    QString mBgColorStr;

    ContextItemContainer *mContextItemContainer;

    // 代码片段容器管理
    QVBoxLayout *mSnippetContainerLayout;
    QList<CodeSnippetWidget*> mCodeSnippetWidgets; // 管理多个代码片段控件
    bool mShowSnippet;

    CustomTextEdit *mTextEdit;
    bool mInStreaming;

    QToolBar *mToolBar;
    QAction *mUseCurrentFileAction;
    QAction *mFocusEditorAction;
    QCheckBox *mUserCurrentFileChk;
    QAction *mUserCurrentFileAction;
    QAction *mContextBuilderAction;

};

}

#endif // INPUTWIDGET_H
