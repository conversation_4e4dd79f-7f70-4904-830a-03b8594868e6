#ifndef CONTEXTBUILDERWGT_H
#define CONTEXTBUILDERWGT_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QTextEdit>
#include <QPlainTextEdit>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QTimer>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QFileInfo>
#include <QDir>
#include <QLineEdit>
#include <QRadioButton>
#include <QToolBar>
#include <QAction>

#include "chatcontext/contextitem.h"

namespace CodeBooster::Internal {

class GitIgnoreParser;

/**
 * @brief 上下文导航树项，用于在树形控件中显示上下文项
 */
class ContextTreeItem : public QTreeWidgetItem
{
public:
    explicit ContextTreeItem(const ContextItem &context, QTreeWidget *parent = nullptr);
    explicit ContextTreeItem(const ContextItem &context, QTreeWidgetItem *parent = nullptr);

    ContextItem contextItem() const { return mContextItem; }
    void setContextItem(const ContextItem &context) { mContextItem = context; }

    bool isChecked() const;
    void setChecked(bool checked);

private:
    ContextItem mContextItem;
};

/**
 * @brief 支持拖拽的上下文导航树
 */
class ContextNavigationTree : public QTreeWidget
{
    Q_OBJECT

public:
    explicit ContextNavigationTree(QWidget *parent = nullptr);
    ~ContextNavigationTree();

    void addContextItem(const ContextItem &context);
    void removeContextItem(const QString &itemId);
    void clearContextItems();

    QList<ContextItem> getSelectedContextItems() const;
    QList<ContextItem> getAllContextItems() const;

    void setGitIgnoreRules(const QString &rules);
    void setIncludeSubfolders(bool include);

    void selectAll();
    void selectNone();
    void invertSelection();
    void refreshContextItems();

    QToolBar* getToolBar() const { return mToolBar; }

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private slots:
    void onItemChanged(QTreeWidgetItem *item, int column);
    void onCustomContextMenuRequested(const QPoint &pos);
    void onDeleteItemClicked();

private:
    void setupContextMenu();
    void setupToolBar();
    void processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix = "", bool isLast = true);
    void updateItemActions();

signals:
    void contextItemsChanged();

private:
    GitIgnoreParser *mGitIgnoreParser;
    bool mIncludeSubfolders;

    // 工具栏和操作
    QToolBar *mToolBar;
    QAction *mDeleteAllAction;
    QAction *mSelectAllAction;
    QAction *mSelectNoneAction;
    QAction *mInvertSelectionAction;
    QAction *mRefreshAction;

    // 右键菜单
    QAction *mDeleteAction;
    QAction *mToggleCheckAction;
};

/**
 * @brief 上下文构建器主控件
 */
class ContextBuilderWgt : public QWidget
{
    Q_OBJECT

public:
    explicit ContextBuilderWgt(QWidget *parent = nullptr);
    ~ContextBuilderWgt();

    void addContextItem(const ContextItem &context);
    void clearContextItems();

private slots:
    void onInstructionTextChanged();
    void onUpdatePromptTimer();
    void onSendToChat();
    void onContextItemsChanged();
    void onFilterRulesChanged();
    void onIncludeSubfoldersChanged();
    void onResetFileExtensions();
    void onResetFolderRules();

private:
    void setupUI();
    void setupConnections();
    void updateFinalPrompt();
    QString buildFinalPrompt() const;

signals:
    void sendContextToChat(const QList<ContextItem> &contexts);

private:
    // UI 组件
    QVBoxLayout *mMainLayout;

    // 文件夹内容过滤选项区域
    QGroupBox *mFilterGroupBox;
    QRadioButton *mIncludeSubfoldersYes;
    QRadioButton *mIncludeSubfoldersNo;
    QLineEdit *mFileExtensionsEdit;
    QLineEdit *mFolderRulesEdit;
    QPushButton *mResetFileExtensionsBtn;
    QPushButton *mResetFolderRulesBtn;

    // 上下文导航树区域
    QGroupBox *mContextGroupBox;
    ContextNavigationTree *mContextTree;
    QLabel *mContextInfoLabel;

    // 指令输入框
    QGroupBox *mInstructionGroupBox;
    QTextEdit *mInstructionEdit;

    // 最终Prompt框
    QGroupBox *mPromptGroupBox;
    QPlainTextEdit *mFinalPromptEdit;

    // 操作按钮
    QHBoxLayout *mButtonLayout;
    QPushButton *mSendToChatBtn;
    QPushButton *mClearAllBtn;

    // 定时器
    QTimer *mUpdatePromptTimer;

    // 其他
    GitIgnoreParser *mGitIgnoreParser;
};

} // namespace CodeBooster::Internal

#endif // CONTEXTBUILDERWGT_H
