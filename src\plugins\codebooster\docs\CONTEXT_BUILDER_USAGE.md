# 上下文构建器使用说明

## 概述

上下文构建器是CodeBooster插件的一个重要功能，类似于16x Prompt工具，允许用户通过图形界面构建复杂的上下文信息，然后发送到AI聊天界面。

## 主要功能

### 1. 文件夹内容过滤选项
- 支持.gitignore格式的过滤规则
- 可以自定义过滤规则或使用默认规则
- 实时应用过滤规则到拖拽的文件夹内容

### 2. 上下文导航树
- 支持拖拽文件和文件夹到树形控件
- 显示文件夹的树形结构
- 支持勾选/取消勾选上下文项
- 右键菜单支持删除和切换勾选状态
- 显示上下文统计信息（文件夹数量、文件数量）

### 3. 指令输入框
- 输入用户的提示词指令
- 支持2秒延迟自动更新最终Prompt
- 多行文本输入

### 4. 最终Prompt框
- 只读显示最终组合的Prompt内容
- 包含上下文信息和用户指令
- 自动格式化为Markdown格式

### 5. 操作按钮
- "发送到对话框"：将选中的上下文发送到聊天界面
- "清空所有"：清除所有上下文和指令

## 使用方法

### 打开上下文构建器
1. 在聊天侧边栏的输入区域，点击工具栏中的"上下文生成器"按钮
2. 系统会切换到上下文构建器页面

### 添加上下文
1. **拖拽文件**：直接将文本文件拖拽到上下文导航树区域
2. **拖拽文件夹**：将文件夹拖拽到上下文导航树区域，系统会自动处理文件夹内的所有文本文件

### 配置过滤规则
1. 在"文件夹内容过滤规则"区域输入.gitignore格式的规则
2. 点击"重置为默认"按钮使用内置的默认规则
3. 点击"应用规则"按钮应用自定义规则

### 输入指令
1. 在"指令输入"区域输入您的提示词
2. 系统会在停止输入2秒后自动更新最终Prompt

### 发送到聊天
1. 确保已选中需要的上下文项（默认全部选中）
2. 点击"发送到对话框"按钮
3. 系统会自动切换回聊天页面并将上下文添加到输入框

## 技术特性

### 文件过滤
- 自动过滤非文本文件
- 支持.gitignore格式的高级过滤规则
- 默认过滤构建目录、临时文件、隐藏文件等

### 上下文格式
- 文件上下文包含文件路径、文件名和内容
- 文件夹上下文包含文件夹路径、树形结构和所有文件内容
- 自动推断文件类型并添加语法高亮标记

### 性能优化
- 使用2秒延迟避免频繁更新
- 支持大文件夹的递归处理
- 内存友好的文件读取方式

## 默认过滤规则

```gitignore
# 构建目录
build/
Build/
BUILD/

# Qt 用户文件
*.user
*.user.*

# 隐藏文件和目录（以点开头）
.*

# 常见的临时文件和缓存
*.tmp
*.temp
*.cache
*.log

# 编译产物
*.o
*.obj
*.exe
*.dll
*.so
*.dylib

# IDE 相关
.vscode/
.idea/
*.pro.user
```

## 注意事项

1. 只支持文本文件，二进制文件会被自动过滤
2. 大文件夹可能需要一些时间来处理
3. 过滤规则的修改需要点击"应用规则"按钮才会生效
4. 上下文构建器的状态不会在会话间保持，每次打开都是空白状态
