#include "contextbuilderwgt.h"

#include <QApplication>
#include <QClipboard>
#include <QHeaderView>
#include <QMenu>
#include <QMessageBox>
#include <QSplitter>
#include <QScrollArea>

#include "common/codeboosterutils.h"
#include "common/widgettheme.h"
#include "pluginsettings/codeboostersettings.h"
#include "utility/gitignoreparser.h"

namespace CodeBooster::Internal {

// -------------------------------------------------------------------------
// ContextTreeItem 实现
// -------------------------------------------------------------------------

ContextTreeItem::ContextTreeItem(const ContextItem &context, QTreeWidget *parent)
    : QTreeWidgetItem(parent), mContextItem(context)
{
    setText(0, context.name);
    setIcon(0, context.icon());
    setCheckState(0, Qt::Checked);
    setToolTip(0, context.description);

    // 如果是文件夹类型，显示树形结构
    if (context.type == ContextItem::Folder && !context.treeStructure.isEmpty()) {
        setToolTip(0, context.treeStructure);
    }
}

ContextTreeItem::ContextTreeItem(const ContextItem &context, QTreeWidgetItem *parent)
    : QTreeWidgetItem(parent), mContextItem(context)
{
    setText(0, context.name);
    setIcon(0, context.icon());
    setCheckState(0, Qt::Checked);
    setToolTip(0, context.description);

    // 如果是文件夹类型，显示树形结构
    if (context.type == ContextItem::Folder && !context.treeStructure.isEmpty()) {
        setToolTip(0, context.treeStructure);
    }
}

bool ContextTreeItem::isChecked() const
{
    return checkState(0) == Qt::Checked;
}

void ContextTreeItem::setChecked(bool checked)
{
    setCheckState(0, checked ? Qt::Checked : Qt::Unchecked);
}

// -------------------------------------------------------------------------
// ContextNavigationTree 实现
// -------------------------------------------------------------------------

ContextNavigationTree::ContextNavigationTree(QWidget *parent)
    : QTreeWidget(parent), mGitIgnoreParser(nullptr), mIncludeSubfolders(true)
{
    // 设置基本属性
    setHeaderLabels(QStringList() << "项目" << "操作");
    setRootIsDecorated(true);
    setAlternatingRowColors(true);
    setSelectionMode(QAbstractItemView::ExtendedSelection);
    setDragDropMode(QAbstractItemView::DropOnly);
    setAcceptDrops(true);

    // 设置列宽
    header()->setStretchLastSection(false);
    header()->setSectionResizeMode(0, QHeaderView::Stretch);
    header()->setSectionResizeMode(1, QHeaderView::ResizeToContents);

    // 启用右键菜单
    setContextMenuPolicy(Qt::CustomContextMenu);
    setupContextMenu();
    setupToolBar();

    // 初始化 GitIgnore 解析器
    mGitIgnoreParser = new GitIgnoreParser();

    // 连接信号
    connect(this, &QTreeWidget::itemChanged, this, &ContextNavigationTree::onItemChanged);
    connect(this, &QTreeWidget::customContextMenuRequested, this, &ContextNavigationTree::onCustomContextMenuRequested);
}

ContextNavigationTree::~ContextNavigationTree()
{
    delete mGitIgnoreParser;
}

void ContextNavigationTree::setupContextMenu()
{
    mDeleteAction = new QAction("删除", this);
    mDeleteAction->setIcon(QIcon(":/icons/delete.png"));
    connect(mDeleteAction, &QAction::triggered, this, [this]() {
        QList<QTreeWidgetItem*> selectedItems = this->selectedItems();
        for (QTreeWidgetItem *item : selectedItems) {
            delete item;
        }
        emit contextItemsChanged();
    });

    mToggleCheckAction = new QAction("切换勾选状态", this);
    connect(mToggleCheckAction, &QAction::triggered, this, [this]() {
        QList<QTreeWidgetItem*> selectedItems = this->selectedItems();
        for (QTreeWidgetItem *item : selectedItems) {
            ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
            if (contextItem) {
                contextItem->setChecked(!contextItem->isChecked());
            }
        }
        emit contextItemsChanged();
    });
}

void ContextNavigationTree::setupToolBar()
{
    mToolBar = new QToolBar(this);
    mToolBar->setToolButtonStyle(Qt::ToolButtonTextOnly);
    mToolBar->setStyleSheet("QToolBar { border: none; background: transparent; }");

    // 删除所有
    mDeleteAllAction = new QAction("删除所有", this);
    connect(mDeleteAllAction, &QAction::triggered, this, &ContextNavigationTree::clearContextItems);
    mToolBar->addAction(mDeleteAllAction);

    // 全选
    mSelectAllAction = new QAction("全选", this);
    connect(mSelectAllAction, &QAction::triggered, this, &ContextNavigationTree::selectAll);
    mToolBar->addAction(mSelectAllAction);

    // 全不选
    mSelectNoneAction = new QAction("全不选", this);
    connect(mSelectNoneAction, &QAction::triggered, this, &ContextNavigationTree::selectNone);
    mToolBar->addAction(mSelectNoneAction);

    // 反选
    mInvertSelectionAction = new QAction("反选", this);
    connect(mInvertSelectionAction, &QAction::triggered, this, &ContextNavigationTree::invertSelection);
    mToolBar->addAction(mInvertSelectionAction);

    // 刷新
    mRefreshAction = new QAction("刷新", this);
    connect(mRefreshAction, &QAction::triggered, this, &ContextNavigationTree::refreshContextItems);
    mToolBar->addAction(mRefreshAction);

    updateItemActions();
}

void ContextNavigationTree::addContextItem(const ContextItem &context)
{
    // 检查是否已存在相同的项
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *existingItem = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (existingItem && existingItem->contextItem().itemId == context.itemId) {
            // 已存在，更新内容
            existingItem->setContextItem(context);
            existingItem->setText(0, context.name);
            existingItem->setIcon(0, context.icon());
            existingItem->setToolTip(0, context.description);
            updateItemActions();
            emit contextItemsChanged();
            return;
        }
    }

    // 不存在，添加新项
    ContextTreeItem *newItem = new ContextTreeItem(context, this);
    addTopLevelItem(newItem);

    // 在第二列添加删除按钮
    QPushButton *deleteBtn = new QPushButton("删除", this);
    deleteBtn->setMaximumSize(60, 25);
    deleteBtn->setProperty("contextItemId", context.itemId);
    connect(deleteBtn, &QPushButton::clicked, this, &ContextNavigationTree::onDeleteItemClicked);
    setItemWidget(newItem, 1, deleteBtn);

    updateItemActions();
    emit contextItemsChanged();
}

void ContextNavigationTree::removeContextItem(const QString &itemId)
{
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->contextItem().itemId == itemId) {
            delete takeTopLevelItem(i);
            emit contextItemsChanged();
            return;
        }
    }
}

void ContextNavigationTree::clearContextItems()
{
    clear();
    updateItemActions();
    emit contextItemsChanged();
}

QList<ContextItem> ContextNavigationTree::getSelectedContextItems() const
{
    QList<ContextItem> selectedContexts;

    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->isChecked()) {
            selectedContexts.append(item->contextItem());
        }
    }

    return selectedContexts;
}

QList<ContextItem> ContextNavigationTree::getAllContextItems() const
{
    QList<ContextItem> allContexts;

    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item) {
            allContexts.append(item->contextItem());
        }
    }

    return allContexts;
}

void ContextNavigationTree::setGitIgnoreRules(const QString &rules)
{
    if (mGitIgnoreParser) {
        mGitIgnoreParser->setIgnoreRules(rules);
    }
}

void ContextNavigationTree::setIncludeSubfolders(bool include)
{
    mIncludeSubfolders = include;
}

void ContextNavigationTree::selectAll()
{
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item) {
            item->setChecked(true);
        }
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::selectNone()
{
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item) {
            item->setChecked(false);
        }
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::invertSelection()
{
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item) {
            item->setChecked(!item->isChecked());
        }
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::refreshContextItems()
{
    // 刷新所有文件夹类型的上下文项
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->contextItem().type == ContextItem::Folder) {
            ContextItem context = item->contextItem();

            // 重新处理文件夹
            QString content;
            int fileCount = 0;
            QString treeStructure;

            QFileInfo folderInfo(context.uri);
            if (folderInfo.exists() && folderInfo.isDir()) {
                treeStructure += ".\n";
                treeStructure += "└── " + folderInfo.fileName() + "/\n";

                processDirectory(context.uri, content, fileCount, treeStructure, "    ");

                context.content = content;
                context.fileCount = fileCount;
                context.treeStructure = treeStructure;

                item->setContextItem(context);
                item->setToolTip(0, treeStructure);
            }
        }
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::updateItemActions()
{
    bool hasItems = topLevelItemCount() > 0;

    if (mDeleteAllAction) mDeleteAllAction->setEnabled(hasItems);
    if (mSelectAllAction) mSelectAllAction->setEnabled(hasItems);
    if (mSelectNoneAction) mSelectNoneAction->setEnabled(hasItems);
    if (mInvertSelectionAction) mInvertSelectionAction->setEnabled(hasItems);
    if (mRefreshAction) mRefreshAction->setEnabled(hasItems);
}

void ContextNavigationTree::dragEnterEvent(QDragEnterEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir()) {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile()) {
                    if (fileIsTextFile(path)) {
                        hasValidFiles = true;
                    } else {
                        // 发现非文本文件，拒绝整个拖拽操作
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles) {
            event->acceptProposedAction();
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void ContextNavigationTree::dragMoveEvent(QDragMoveEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir()) {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile()) {
                    if (fileIsTextFile(path)) {
                        hasValidFiles = true;
                    } else {
                        // 发现非文本文件，拒绝拖拽
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles) {
            event->acceptProposedAction();
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void ContextNavigationTree::dropEvent(QDropEvent *event)
{
    // 获取拖拽进来的数据
    const QMimeData *mimeData = event->mimeData();

    // 处理拖拽的URL数据
    if (mimeData->hasUrls()) {
        QList<QUrl> urlList = mimeData->urls();
        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 拖进来单个文件
                if (fileInfo.isFile()) {
                    // 检查文件是否为文本文件
                    if (!fileIsTextFile(path)) {
                        continue; // 跳过非文本文件
                    }

                    bool success = false;
                    ContextItem item = ContextItem::buildFileContextFromFilePath(Utils::FilePath::fromString(path), success);
                    if (success) {
                        addContextItem(item);
                    }
                }
                // 拖进来文件夹
                else if (fileInfo.isDir()) {
                    QString content;
                    int fileCount = 0;
                    QString treeStructure;

                    // 添加根目录到树形结构，使用点作为根标识
                    treeStructure += ".\n";
                    treeStructure += "└── " + fileInfo.fileName() + "/\n";

                    processDirectory(path, content, fileCount, treeStructure, "    ");

                    ContextItem item;
                    item.name = fileInfo.fileName();
                    item.description = fileInfo.path();
                    item.content = content;
                    item.type = ContextItem::Folder;
                    item.uri = path;
                    item.fileCount = fileCount;
                    item.treeStructure = treeStructure;

                    addContextItem(item);
                }
            }
        }
    }

    // 接受拖拽事件
    event->accept();
}

void ContextNavigationTree::onItemChanged(QTreeWidgetItem *item, int column)
{
    Q_UNUSED(column)
    Q_UNUSED(item)
    emit contextItemsChanged();
}

void ContextNavigationTree::onCustomContextMenuRequested(const QPoint &pos)
{
    QTreeWidgetItem *item = itemAt(pos);
    if (!item) {
        return;
    }

    QMenu contextMenu(this);
    contextMenu.addAction(mDeleteAction);
    contextMenu.addAction(mToggleCheckAction);

    contextMenu.exec(mapToGlobal(pos));
}

void ContextNavigationTree::onDeleteItemClicked()
{
    QPushButton *button = qobject_cast<QPushButton*>(sender());
    if (!button) return;

    QString itemId = button->property("contextItemId").toString();
    removeContextItem(itemId);
}

// 递归处理文件夹的函数（复用CustomTextEdit的逻辑）
void ContextNavigationTree::processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix, bool isLast)
{
    QDir dir(path);
    QStringList entryList = dir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    // 分离文件和目录，并排序
    QStringList files, directories;
    for (const QString &entry : entryList) {
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);

        if (fileInfo.isFile()) {
            // 检查文件是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreFile(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略文件: %1").arg(fullPath));
                continue;
            }
            if (!fileIsTextFile(fullPath)) {
                continue;
            }
            files.append(entry);
        } else if (fileInfo.isDir()) {
            // 检查目录是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreDirectory(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略目录: %1").arg(fullPath));
                continue;
            }
            directories.append(entry);
        }
    }

    // 排序
    files.sort();
    directories.sort();

    // 合并列表，目录在前，文件在后
    QStringList allEntries = directories + files;

    for (int i = 0; i < allEntries.size(); ++i) {
        const QString &entry = allEntries.at(i);
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);
        bool isLastEntry = (i == allEntries.size() - 1);

        // 构建树形结构字符串
        QString connector = isLastEntry ? "└── " : "├── ";
        treeStructure += prefix + connector + entry;

        if (fileInfo.isDir()) {
            treeStructure += "/\n";

            // 只有在启用子文件夹时才递归处理
            if (mIncludeSubfolders) {
                QString nextPrefix = prefix + (isLastEntry ? "    " : "│   ");
                processDirectory(fullPath, content, fileCount, treeStructure, nextPrefix, isLastEntry);
            }
        } else {
            treeStructure += "\n";

            // 读取文件内容
            bool success = false;
            QString fileContent = Internal::readTextFile(fullPath, success);
            if (success) {
                content += QString("=== %1 ===\n").arg(entry);
                content += fileContent;
                content += "\n\n";
                fileCount++;
            }
        }
    }
}

// -------------------------------------------------------------------------
// ContextBuilderWgt 实现
// -------------------------------------------------------------------------

ContextBuilderWgt::ContextBuilderWgt(QWidget *parent)
    : QWidget(parent)
    , mMainLayout(nullptr)
    , mFilterGroupBox(nullptr)
    , mIncludeSubfoldersYes(nullptr)
    , mIncludeSubfoldersNo(nullptr)
    , mFileExtensionsEdit(nullptr)
    , mFolderRulesEdit(nullptr)
    , mResetFileExtensionsBtn(nullptr)
    , mResetFolderRulesBtn(nullptr)
    , mContextGroupBox(nullptr)
    , mContextTree(nullptr)
    , mContextInfoLabel(nullptr)
    , mInstructionGroupBox(nullptr)
    , mInstructionEdit(nullptr)
    , mPromptGroupBox(nullptr)
    , mFinalPromptEdit(nullptr)
    , mButtonLayout(nullptr)
    , mSendToChatBtn(nullptr)
    , mClearAllBtn(nullptr)
    , mUpdatePromptTimer(nullptr)
    , mGitIgnoreParser(nullptr)
{
    setupUI();
    setupConnections();

    // 初始化 GitIgnore 解析器
    mGitIgnoreParser = new GitIgnoreParser();

    // 初始化定时器
    mUpdatePromptTimer = new QTimer(this);
    mUpdatePromptTimer->setSingleShot(true);
    mUpdatePromptTimer->setInterval(2000); // 2秒延迟

    // 初始化默认过滤规则（在UI创建之后）
    if (mFileExtensionsEdit && mFolderRulesEdit) {
        mFileExtensionsEdit->setText(".env,.log,.gitignore,.json,.o");
        mFolderRulesEdit->setText(".git/,.svn/,.vscode/,.idea/,node_modules/,venv/");
        onFilterRulesChanged();
    }
}

ContextBuilderWgt::~ContextBuilderWgt()
{
    delete mGitIgnoreParser;
}

void ContextBuilderWgt::setupUI()
{
    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setContentsMargins(8, 8, 8, 8);
    mMainLayout->setSpacing(8);

    // 1. 文件夹内容过滤选项区域
    mFilterGroupBox = new QGroupBox("文件夹过滤选项", this);
    {
        QVBoxLayout *filterLayout = new QVBoxLayout(mFilterGroupBox);

        // 读取子文件夹选项
        QHBoxLayout *subfolderLayout = new QHBoxLayout();
        QLabel *subfolderLabel = new QLabel("读取子文件夹：", this);
        mIncludeSubfoldersYes = new QRadioButton("是", this);
        mIncludeSubfoldersNo = new QRadioButton("否", this);
        mIncludeSubfoldersYes->setChecked(true); // 默认选中

        subfolderLayout->addWidget(subfolderLabel);
        subfolderLayout->addWidget(mIncludeSubfoldersYes);
        subfolderLayout->addWidget(mIncludeSubfoldersNo);
        subfolderLayout->addStretch();

        // 忽略的文件后缀
        QHBoxLayout *fileExtLayout = new QHBoxLayout();
        QLabel *fileExtLabel = new QLabel("忽略的文件后缀（使用逗号分隔）：", this);
        filterLayout->addWidget(fileExtLabel);

        QHBoxLayout *fileExtInputLayout = new QHBoxLayout();
        mFileExtensionsEdit = new QLineEdit(this);
        mFileExtensionsEdit->setPlaceholderText(".env,.log,.gitignore,.json,.o");
        mResetFileExtensionsBtn = new QPushButton("重置", this);
        fileExtInputLayout->addWidget(mFileExtensionsEdit);
        fileExtInputLayout->addWidget(mResetFileExtensionsBtn);

        // 忽略的文件夹
        QLabel *folderLabel = new QLabel("忽略的文件夹名（使用逗号分隔）：", this);
        filterLayout->addWidget(folderLabel);

        QHBoxLayout *folderInputLayout = new QHBoxLayout();
        mFolderRulesEdit = new QLineEdit(this);
        mFolderRulesEdit->setPlaceholderText(".git/,.svn/,.vscode/,.idea/,node_modules/,venv/");
        mResetFolderRulesBtn = new QPushButton("重置", this);
        folderInputLayout->addWidget(mFolderRulesEdit);
        folderInputLayout->addWidget(mResetFolderRulesBtn);

        // 提示信息
        QLabel *infoLabel = new QLabel("媒体文件、二进制文件和大于 500KB 的文本文件会被自动忽略", this);
        infoLabel->setStyleSheet("color: #666; font-size: 11px;");

        filterLayout->addLayout(subfolderLayout);
        filterLayout->addLayout(fileExtInputLayout);
        filterLayout->addLayout(folderInputLayout);
        filterLayout->addWidget(infoLabel);
    }

    // 2. 上下文导航树区域
    mContextGroupBox = new QGroupBox("上下文", this);
    {
        QVBoxLayout *contextLayout = new QVBoxLayout(mContextGroupBox);

        mContextInfoLabel = new QLabel("拖拽文件或文件夹到此处添加上下文", this);
        mContextInfoLabel->setStyleSheet("color: #666; font-style: italic;");
        mContextInfoLabel->setAlignment(Qt::AlignCenter);

        mContextTree = new ContextNavigationTree(this);
        mContextTree->setMinimumHeight(200);

        // 添加工具栏到上下文树的上方
        contextLayout->addWidget(mContextInfoLabel);
        contextLayout->addWidget(mContextTree->getToolBar());
        contextLayout->addWidget(mContextTree);
    }

    // 3. 指令输入框
    mInstructionGroupBox = new QGroupBox("指令输入", this);
    {
        QVBoxLayout *instructionLayout = new QVBoxLayout(mInstructionGroupBox);

        mInstructionEdit = new QTextEdit(this);
        mInstructionEdit->setMaximumHeight(100);
        mInstructionEdit->setPlaceholderText("输入您的提示词指令...");

        instructionLayout->addWidget(mInstructionEdit);
    }

    // 4. 最终Prompt框
    mPromptGroupBox = new QGroupBox("最终 Prompt", this);
    {
        QVBoxLayout *promptLayout = new QVBoxLayout(mPromptGroupBox);

        mFinalPromptEdit = new QPlainTextEdit(this);
        mFinalPromptEdit->setReadOnly(true);
        mFinalPromptEdit->setMinimumHeight(150);
        mFinalPromptEdit->setPlaceholderText("最终的 Prompt 内容将在这里显示...");

        promptLayout->addWidget(mFinalPromptEdit);
    }

    // 5. 操作按钮
    mButtonLayout = new QHBoxLayout();
    {
        mSendToChatBtn = new QPushButton("发送到对话框", this);
        mSendToChatBtn->setEnabled(false);

        mClearAllBtn = new QPushButton("清空所有", this);

        mButtonLayout->addStretch();
        mButtonLayout->addWidget(mClearAllBtn);
        mButtonLayout->addWidget(mSendToChatBtn);
    }

    // 添加到主布局
    mMainLayout->addWidget(mFilterGroupBox);
    mMainLayout->addWidget(mContextGroupBox, 1); // 给上下文区域更多空间
    mMainLayout->addWidget(mInstructionGroupBox);
    mMainLayout->addWidget(mPromptGroupBox, 1); // 给Prompt区域更多空间
    mMainLayout->addLayout(mButtonLayout);
}

void ContextBuilderWgt::setupConnections()
{
    // 过滤规则相关
    connect(mIncludeSubfoldersYes, &QRadioButton::toggled, this, &ContextBuilderWgt::onIncludeSubfoldersChanged);
    connect(mIncludeSubfoldersNo, &QRadioButton::toggled, this, &ContextBuilderWgt::onIncludeSubfoldersChanged);

    connect(mResetFileExtensionsBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onResetFileExtensions);
    connect(mResetFolderRulesBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onResetFolderRules);

    connect(mFileExtensionsEdit, &QLineEdit::textChanged, this, &ContextBuilderWgt::onFilterRulesChanged);
    connect(mFolderRulesEdit, &QLineEdit::textChanged, this, &ContextBuilderWgt::onFilterRulesChanged);

    // 指令输入相关
    connect(mInstructionEdit, &QTextEdit::textChanged, this, &ContextBuilderWgt::onInstructionTextChanged);

    // 上下文树相关
    connect(mContextTree, &ContextNavigationTree::contextItemsChanged, this, &ContextBuilderWgt::onContextItemsChanged);

    // 定时器相关
    connect(mUpdatePromptTimer, &QTimer::timeout, this, &ContextBuilderWgt::onUpdatePromptTimer);

    // 按钮相关
    connect(mSendToChatBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onSendToChat);
    connect(mClearAllBtn, &QPushButton::clicked, this, &ContextBuilderWgt::clearContextItems);
}

void ContextBuilderWgt::addContextItem(const ContextItem &context)
{
    mContextTree->addContextItem(context);
}

void ContextBuilderWgt::clearContextItems()
{
    mContextTree->clearContextItems();
    mInstructionEdit->clear();
    mFinalPromptEdit->clear();
    mContextInfoLabel->setText("拖拽文件或文件夹到此处添加上下文");
}

void ContextBuilderWgt::onInstructionTextChanged()
{
    // 重启定时器
    mUpdatePromptTimer->stop();
    mUpdatePromptTimer->start();
}

void ContextBuilderWgt::onUpdatePromptTimer()
{
    updateFinalPrompt();
}

void ContextBuilderWgt::onContextItemsChanged()
{
    updateFinalPrompt();

    // 更新上下文信息标签
    QList<ContextItem> allContexts = mContextTree->getAllContextItems();
    if (allContexts.isEmpty()) {
        mContextInfoLabel->setText("拖拽文件或文件夹到此处添加上下文");
    } else {
        int totalFiles = 0;
        int totalFolders = 0;
        for (const ContextItem &context : allContexts) {
            if (context.type == ContextItem::File) {
                totalFiles++;
            } else if (context.type == ContextItem::Folder) {
                totalFolders++;
                totalFiles += context.fileCount; // 文件夹中的文件数
            }
        }
        mContextInfoLabel->setText(QString("共 %1 个上下文项（%2 个文件夹，%3 个文件）")
                                  .arg(allContexts.size()).arg(totalFolders).arg(totalFiles));
    }
}

void ContextBuilderWgt::onSendToChat()
{
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();
    if (!selectedContexts.isEmpty()) {
        emit sendContextToChat(selectedContexts);
    }
}

void ContextBuilderWgt::onFilterRulesChanged()
{
    // 检查UI是否已初始化
    if (!mFileExtensionsEdit || !mFolderRulesEdit) {
        return;
    }

    // 构建过滤规则
    QString rules;

    // 添加文件后缀规则
    QString fileExtensions = mFileExtensionsEdit->text().trimmed();
    if (!fileExtensions.isEmpty()) {
        QStringList extensions = fileExtensions.split(',', Qt::SkipEmptyParts);
        for (QString ext : extensions) {
            ext = ext.trimmed();
            if (!ext.isEmpty()) {
                if (!ext.startsWith('*')) {
                    ext = "*" + ext;
                }
                rules += ext + "\n";
            }
        }
    }

    // 添加文件夹规则
    QString folderRules = mFolderRulesEdit->text().trimmed();
    if (!folderRules.isEmpty()) {
        QStringList folders = folderRules.split(',', Qt::SkipEmptyParts);
        for (QString folder : folders) {
            folder = folder.trimmed();
            if (!folder.isEmpty()) {
                if (!folder.endsWith('/')) {
                    folder += "/";
                }
                rules += folder + "\n";
            }
        }
    }

    // 如果没有自定义规则，使用默认规则
    if (rules.isEmpty()) {
        rules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
    }

    // 更新GitIgnore解析器
    if (mGitIgnoreParser) {
        mGitIgnoreParser->setIgnoreRules(rules);
    }

    // 更新上下文树的GitIgnore解析器
    if (mContextTree) {
        mContextTree->setGitIgnoreRules(rules);
    }
}

void ContextBuilderWgt::onIncludeSubfoldersChanged()
{
    if (!mIncludeSubfoldersYes || !mContextTree) {
        return;
    }

    bool includeSubfolders = mIncludeSubfoldersYes->isChecked();
    mContextTree->setIncludeSubfolders(includeSubfolders);
}

void ContextBuilderWgt::onResetFileExtensions()
{
    if (mFileExtensionsEdit) {
        mFileExtensionsEdit->setText(".env,.log,.gitignore,.json,.o");
        onFilterRulesChanged();
    }
}

void ContextBuilderWgt::onResetFolderRules()
{
    if (mFolderRulesEdit) {
        mFolderRulesEdit->setText(".git/,.svn/,.vscode/,.idea/,node_modules/,venv/");
        onFilterRulesChanged();
    }
}

void ContextBuilderWgt::updateFinalPrompt()
{
    QString finalPrompt = buildFinalPrompt();
    mFinalPromptEdit->setPlainText(finalPrompt);

    // 更新发送按钮状态
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();
    QString instruction = mInstructionEdit->toPlainText().trimmed();
    mSendToChatBtn->setEnabled(!selectedContexts.isEmpty() || !instruction.isEmpty());
}

QString ContextBuilderWgt::buildFinalPrompt() const
{
    QString instruction = mInstructionEdit->toPlainText().trimmed();
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();

    if (instruction.isEmpty() && selectedContexts.isEmpty()) {
        return QString();
    }

    QString finalPrompt;

    // 添加上下文信息
    if (!selectedContexts.isEmpty()) {
        finalPrompt += ContextItem::contextsToString(selectedContexts);
        finalPrompt += "\n";
    }

    // 添加用户指令
    if (!instruction.isEmpty()) {
        finalPrompt += "# User Message\n";
        finalPrompt += instruction;
        finalPrompt += "\n";
    }

    return finalPrompt;
}

} // namespace CodeBooster::Internal
